import React from "react";
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  Image,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";

const { width } = Dimensions.get("window");
const ARTWORK_SIZE = width * 0.65;

export default function MusicPlayerScreen() {
  return (
    <LinearGradient colors={["#1B0F3F", "#2B0E53"]} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <Image
          source={{
            uri: "https://www.radyoyakamoz.com.tr/mobileapp-yakamoz.png",
          }} // Replace with actual image
          style={styles.artwork}
        />

        {/* Track Info */}
        <View style={styles.trackInfo}>
          <Text style={styles.trackTitle}>Radyo <PERSON>kamoz</Text>
          <Text style={styles.trackSubtitle}>Nostalji.</Text>
        </View>

        {/* Waveform & Progress */}
        <View style={styles.progressContainer}>
          <View style={styles.waveform}>
            {[...Array(20)].map((_, i) => (
              <View
                key={i}
                style={[styles.waveBar, { height: Math.random() * 30 + 10 }]}
              />
            ))}
          </View>
        </View>

        {/* Playback Controls */}
        <View style={styles.controls}>
          <Ionicons name="play-skip-back" size={32} color="#FFFFFF" />

          <View style={styles.playButtonContainer}>
            <LinearGradient
              colors={["#6A35D1", "#4A1D9B"]}
              style={styles.playButtonBackground}
            />
            <Ionicons name="play" size={48} color="#FFFFFF" />
          </View>

          <Ionicons name="play-skip-forward" size={32} color="#FFFFFF" />
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    marginHorizontal: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  artwork: {
    width: ARTWORK_SIZE,
    height: ARTWORK_SIZE,
    borderRadius: 16,
    alignSelf: "center",
    marginTop: 32,
  },
  trackInfo: {
    alignItems: "center",
    marginTop: 24,
  },
  trackTitle: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  trackSubtitle: {
    color: "rgba(255,255,255,0.8)",
    fontSize: 16,
  },
  progressContainer: {
    marginTop: 24,
  },
  timeLabels: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  timeText: {
    color: "#FFFFFF",
    fontSize: 14,
  },
  waveform: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    height: 40,
  },
  waveBar: {
    width: 3,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginHorizontal: 1,
  },
  controls: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 40,
    paddingHorizontal: 24,
  },
  playButtonContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
  },
  playButtonBackground: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    opacity: 0.8,
  },
});
