import React, { useEffect, useRef, useState } from "react";
import {
  SafeAreaView,
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
  Alert,
} from "react-native";
import Svg, { Defs, RadialGradient, Stop, Rect } from "react-native-svg";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Audio } from "expo-av";
import { Easing } from "react-native";
import headphonesImg from "../assets/images/headphones.png";
import radioImg from "../assets/images/mobileapp-yakamoz.png";

const { width } = Dimensions.get("window");

// Improved seamless radio wave animation
const SeamlessRadioWaves = ({ size = 300, color = "#7C4DFF" }) => {
  const wave1 = useRef(new Animated.Value(0)).current;
  const wave2 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateWave = (wave, delay) => {
      wave.setValue(0);
      Animated.loop(
        Animated.sequence([
          Animated.timing(wave, {
            toValue: 1,
            duration: 3500,
            delay,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };
    animateWave(wave1, 0);
  }, [wave1]);

  const getWaveStyle = (wave) => ({
    position: "absolute",
    width: 350,
    height: 350,
    borderRadius: size / 1,
    borderWidth: 3,
    borderColor: color,
    opacity: wave.interpolate({ inputRange: [0, 1], outputRange: [0.2, 0] }),
    transform: [
      {
        scale: wave.interpolate({
          inputRange: [0, 1],
          outputRange: [0.5, 2.7],
        }),
      },
    ],
  });

  return (
    <View
      style={{
        position: "absolute",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Animated.View style={getWaveStyle(wave1)} />
    </View>
  );
};

const WAVE_BAR_COUNT = 20;

const App = () => {
  const [sound, setSound] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPlayer, setShowPlayer] = useState(false);
  const [isStreamStarting, setIsStreamStarting] = useState(false);
  const fadeOutAnim = useRef(new Animated.Value(1)).current;
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const headphoneAnim = useRef(new Animated.Value(0)).current;
  const [reconnectTries, setReconnectTries] = useState(0);
  const waveformAnim = useRef(new Animated.Value(0)).current;

  // Headphone float animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(headphoneAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(headphoneAnim, {
          toValue: 0,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [headphoneAnim]);

  // Audio config
  useEffect(() => {
    const configureAudio = async () => {
      try {
        await Audio.setAudioModeAsync({
          staysActiveInBackground: true,
          playsInSilentModeIOS: true,
          interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
          interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
          shouldDuckAndroid: true,
        });
      } catch (err) {
        console.log("Audio Mode Error:", err);
      }
    };
    configureAudio();
  }, []);

  // Waveform animation (fake, but dynamic)
  useEffect(() => {
    Animated.loop(
      Animated.timing(waveformAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.linear,
        useNativeDriver: false,
      })
    ).start();
  }, []);

  // Auto-reconnect logic
  useEffect(() => {
    if (!isPlaying && showPlayer && !isLoading) {
      // Try to reconnect after 2 seconds if not playing
      const timeout = setTimeout(() => {
        if (!isPlaying && showPlayer) {
          handleStart(true);
        }
      }, 2000);
      return () => clearTimeout(timeout);
    }
  }, [isPlaying, showPlayer, isLoading]);

  // Handle onboarding -> player transition and stream start
  const handleStart = async (isReconnect = false) => {
    // Prevent multiple streams from starting
    if (isStreamStarting) return;

    try {
      setIsStreamStarting(true);
      setIsLoading(true);

      if (sound) {
        await sound.unloadAsync();
      }

      // Reset animation values
      fadeInAnim.setValue(0);
      fadeOutAnim.setValue(1);

      // First set showPlayer to true to trigger the transition
      setShowPlayer(true);

      // Start the fade animations
      Animated.parallel([
        Animated.timing(fadeOutAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(fadeInAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();

      const { sound: newSound } = await Audio.Sound.createAsync(
        {
          uri: "https://ssldyg.radyotvonline.com/smil/radyoyakamoz.stream/playlist.m3u8",
        },
        { shouldPlay: true }
      );
      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.error) {
          setIsPlaying(false);
          setReconnectTries((t) => t + 1);
        } else if (status.isPlaying) {
          setIsPlaying(true);
          setReconnectTries(0);
        } else {
          setIsPlaying(false);
        }
      });
      setSound(newSound);
      setIsPlaying(true);
    } catch (err) {
      setIsPlaying(false);
      setReconnectTries((t) => t + 1);
      if (!isReconnect) Alert.alert("Error", "Stream could not be started.");
    } finally {
      setIsLoading(false);
      setIsStreamStarting(false);
    }
  };

  const togglePlayback = async () => {
    if (!sound) return;
    const status = await sound.getStatusAsync();
    if (status.isPlaying) {
      await sound.pauseAsync();
      setIsPlaying(false);
    } else {
      await sound.playAsync();
      setIsPlaying(true);
    }
  };

  // Simple waveform animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(waveformAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
        Animated.timing(waveformAnim, {
          toValue: 0,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
      ])
    ).start();
  }, []);

  // Simple waveform bars
  const renderWaveform = () => {
    return (
      <View style={styles.waveform}>
        {[...Array(WAVE_BAR_COUNT)].map((_, i) => {
          const height = waveformAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 60],
          });
          return <Animated.View key={i} style={[styles.waveBar, { height }]} />;
        })}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <BlurView intensity={100} style={StyleSheet.absoluteFill} tint="dark" />
      <Svg height="100%" width="100%" style={StyleSheet.absoluteFillObject}>
        <Defs>
          <RadialGradient id="grad" cx="50%" cy="50%" rx="80%" ry="50%">
            <Stop offset="0%" stopColor="#9668EF" stopOpacity="1" />
            <Stop offset="100%" stopColor="#100A1C" stopOpacity="1" />
          </RadialGradient>
        </Defs>
        <Rect width="100%" height="100%" fill="url(#grad)" />
      </Svg>

      {/* Onboarding Screen */}
      {!showPlayer && (
        <Animated.View style={[styles.content, { opacity: fadeOutAnim }]}>
          <View style={styles.waveContainer} pointerEvents="none">
            <SeamlessRadioWaves size={300} color="#7C4DFF" />
          </View>
          <Animated.Image
            source={headphonesImg}
            style={[
              styles.headphones,
              {
                transform: [
                  {
                    translateY: headphoneAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -15],
                    }),
                  },
                ],
              },
            ]}
            resizeMode="contain"
          />
          <Text style={styles.bigTitle}>Getting Started</Text>
          <Text style={styles.subtitle}>Getting Started Getting</Text>
          <TouchableOpacity
            style={styles.startButton}
            onPress={handleStart}
            disabled={isLoading}
          >
            <LinearGradient
              colors={["#a18aff", "#7C4DFF"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.gradientButton}
            >
              <Text style={styles.buttonText}>
                {isLoading ? "Yükleniyor..." : "Let'go"}{" "}
              </Text>
              <Ionicons
                name="musical-note"
                size={22}
                color="#fff"
                style={{ marginLeft: 4 }}
              />
            </LinearGradient>
          </TouchableOpacity>
          <View style={styles.footer}>
            <Text style={styles.footerTitle}>Staca</Text>
            <Text style={styles.footerSubtitle}>Rest music app</Text>
          </View>
        </Animated.View>
      )}

      {/* Player Screen */}
      {showPlayer && (
        <Animated.View
          style={[styles.playerContainer, { opacity: fadeInAnim }]}
        >
          {/* Top Left Buttons */}
          <View style={styles.topButtonsContainer}>
            <TouchableOpacity
              style={styles.circleButton}
              onPress={() => handleStart(true)}
              disabled={isLoading}
            >
              <Ionicons name="reload" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.circleButton, styles.reportButton]}
              onPress={() => Alert.alert("Report", "Report an issue")}
            >
              <Ionicons name="alert-circle" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <Image source={radioImg} style={styles.playerArtwork} />
          <View style={styles.trackInfo}>
            <Text style={styles.trackTitle}>Radyo Yakamoz</Text>
            <Text style={styles.trackSubtitle}>Nostalji.</Text>
          </View>
          <View style={styles.progressContainer}>{renderWaveform()}</View>
          <View style={styles.controlsRow}>
            <TouchableOpacity
              onPress={togglePlayback}
              style={styles.iconButton}
            >
              <Ionicons name="play-skip-back" size={32} color="#FFFFFF" />
            </TouchableOpacity>
            <View style={styles.playButtonContainer}>
              <View style={styles.playButtonBackground} />
              <TouchableOpacity onPress={togglePlayback}>
                <Ionicons
                  name={isPlaying ? "pause" : "play"}
                  size={48}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              onPress={togglePlayback}
              style={styles.iconButton}
            >
              <Ionicons name="play-skip-forward" size={32} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#000" },
  content: { flex: 1, justifyContent: "center", alignItems: "center" },
  waveContainer: {
    position: "absolute",
    top: width * 0.66,
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 0,
  },
  headphones: {
    width: width * 0.55,
    height: width * 0.55,
    marginBottom: 30,
    zIndex: 1,
  },
  bigTitle: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
    marginTop: 10,
    textAlign: "center",
  },
  subtitle: {
    color: "#d1cfff",
    fontSize: 16,
    marginTop: 6,
    marginBottom: 40,
    textAlign: "center",
  },
  startButton: {
    marginTop: 10,
    borderRadius: 25,
    overflow: "hidden",
  },
  gradientButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 36,
    paddingVertical: 14,
    borderRadius: 25,
  },
  buttonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  footer: {
    position: "absolute",
    bottom: 32,
    width: "100%",
    alignItems: "center",
  },
  footerTitle: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 18,
    marginBottom: 2,
  },
  footerSubtitle: {
    color: "#d1cfff",
    fontSize: 13,
  },
  playerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  playerArtwork: {
    width: 280,
    height: 280,
    borderRadius: 16,
    alignSelf: "center",
    marginBottom: 4,
    opacity: 1,
  },
  trackInfo: {
    alignItems: "center",
    marginBottom: 0,
  },
  trackTitle: {
    color: "#FFFFFF",
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 5,
  },
  trackSubtitle: {
    color: "rgba(255,255,255,0.8)",
    fontSize: 18,
  },
  progressContainer: {
    width: "100%",
    marginVertical: 5,
  },
  waveform: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    height: 60,
    width: "100%",
    paddingHorizontal: 10,
  },
  waveBar: {
    width: 3,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginHorizontal: 1,
  },
  controlsRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 40,
    gap: 32,
  },
  iconButton: {
    padding: 8,
  },
  playButtonContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 24,
  },
  playButtonBackground: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    opacity: 0.8,
    backgroundColor: "#7C4DFF",
    shadowColor: "#7C4DFF",
    shadowOpacity: 0.6,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: 0 },
  },
  topButtonsContainer: {
    position: "absolute",
    top: 20,
    left: 20,
    zIndex: 10,
  },
  circleButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "rgba(124, 77, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  reportButton: {
    backgroundColor: "rgba(255, 59, 48, 0.8)",
  },
});

export default App;
