import React, { useEffect, useRef, useState } from "react";
import {
  SafeAreaView,
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
} from "react-native";
import Svg, { Defs, RadialGradient, Stop, Rect } from "react-native-svg";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";

const { width } = Dimensions.get("window");

// Improved seamless radio wave animation
const SeamlessRadioWaves = ({ size = 300, color = "#7C4DFF" }) => {
  const wave1 = useRef(new Animated.Value(0)).current;
  const wave2 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateWave = (wave, delay) => {
      wave.setValue(0);
      Animated.loop(
        Animated.sequence([
          Animated.timing(wave, {
            toValue: 1,
            duration: 2200,
            delay,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };
    animateWave(wave1, 0);
    animateWave(wave2, 1100); // Start second wave halfway through the first
  }, [wave1, wave2]);

  const getWaveStyle = (wave) => ({
    position: "absolute",
    width: size,
    height: size,
    borderRadius: size / 2,
    borderWidth: 2,
    borderColor: color,
    opacity: wave.interpolate({ inputRange: [0, 1], outputRange: [0.5, 0] }),
    transform: [
      {
        scale: wave.interpolate({ inputRange: [0, 1], outputRange: [1, 1.7] }),
      },
    ],
  });

  return (
    <View
      style={{
        position: "absolute",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Animated.View style={getWaveStyle(wave1)} />
      <Animated.View style={getWaveStyle(wave2)} />
    </View>
  );
};

export default function OnboardingScreen() {
  const [showPlayer, setShowPlayer] = useState(false);
  const fadeOutAnim = useRef(new Animated.Value(1)).current;
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const flowAnim = useRef(new Animated.Value(0)).current;
  const [isFading, setIsFading] = useState(false);

  // Headphone float animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(flowAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(flowAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [flowAnim]);

  // Handle onboarding -> player transition
  const handleStart = () => {
    setIsFading(true);
    Animated.timing(fadeOutAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      setShowPlayer(true);
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    });
  };

  return (
    <View style={styles.container}>
      {/* SVG Radial Gradient Background */}
      <BlurView intensity={100} style={StyleSheet.absoluteFill} tint="dark" />
      <Svg height="100%" width="100%" style={StyleSheet.absoluteFillObject}>
        <Defs>
          <RadialGradient
            id="grad1"
            cx="0%"
            cy="30%"
            rx="80%"
            ry="50%"
            fx="50%"
            fy="50%"
          >
            <Stop offset="0%" stopColor="#9668EF" stopOpacity="1" />
            <Stop offset="100%" stopColor="#100A1C" stopOpacity="1" />
          </RadialGradient>
          <RadialGradient
            id="grad2"
            cx="100%"
            cy="50%"
            rx="60%"
            ry="60%"
            fx="50%"
            fy="50%"
          >
            <Stop offset="0%" stopColor="#322251" stopOpacity="0.7" />
            <Stop offset="100%" stopColor="#100A1C" stopOpacity="0" />
          </RadialGradient>
          <RadialGradient
            id="grad3"
            cx="50%"
            cy="90%"
            rx="80%"
            ry="49%"
            fx="80%"
            fy="90%"
          >
            <Stop offset="0%" stopColor="#6886EF" stopOpacity="0.5" />
            <Stop offset="100%" stopColor="#100A1C" stopOpacity="0" />
          </RadialGradient>
        </Defs>
        <Rect width="100%" height="100%" fill="url(#grad1)" />
        <Rect width="100%" height="100%" fill="url(#grad2)" />
        <Rect width="100%" height="100%" fill="url(#grad3)" />
      </Svg>
      <SafeAreaView style={styles.safeArea}>
        {/* Onboarding Elements */}
        {!showPlayer && (
          <>
            <Animated.View style={[styles.content, { opacity: fadeOutAnim }]}>
              <View style={styles.imageContainer}>
                <SeamlessRadioWaves size={300} color="#7C4DFF" />
                <Animated.Image
                  source={require("../assets//images/headphones.png")}
                  style={[
                    styles.headphoneImage,
                    {
                      transform: [
                        {
                          translateY: flowAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, -20],
                          }),
                        },
                      ],
                      shadowColor: "#000",
                      shadowOffset: { width: 0, height: 10 },
                      shadowOpacity: 0.5,
                      shadowRadius: 10,
                      elevation: 10,
                    },
                  ]}
                  resizeMode="contain"
                />
              </View>
              <Text style={styles.mainTitle}>Yakamoz Dinle</Text>
              <Text style={styles.subtitle}>
                Yakamoz Dinlemeye Hazırmısınız?!
              </Text>
              <TouchableOpacity
                style={styles.button}
                onPress={handleStart}
                disabled={isFading}
              >
                <Text style={styles.buttonText}>Başla</Text>
                <Ionicons name="musical-notes" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            </Animated.View>
            <Animated.View style={[styles.footer, { opacity: fadeOutAnim }]}>
              <Text style={styles.footerTitle}>M.B.Z.</Text>
              <Text style={styles.footerSubtitle}>yakamoz music app</Text>
            </Animated.View>
          </>
        )}
        {/* Player Controls Elements */}
        {showPlayer && (
          <Animated.View
            style={{ flex: 1, opacity: fadeInAnim, justifyContent: "center" }}
          >
            {/* Artwork */}
            <Image
              source={{
                uri: "https://www.radyoyakamoz.com.tr/mobileapp-yakamoz.png",
              }}
              style={styles.playerArtwork}
            />
            {/* Track Info */}
            <View style={styles.trackInfo}>
              <Text style={styles.trackTitle}>Radyo Yakamoz</Text>
              <Text style={styles.trackSubtitle}>Nostalji.</Text>
            </View>
            {/* Waveform */}
            <View style={styles.progressContainer}>
              <View style={styles.waveform}>
                {[...Array(20)].map((_, i) => (
                  <View
                    key={i}
                    style={[
                      styles.waveBar,
                      { height: Math.random() * 30 + 10 },
                    ]}
                  />
                ))}
              </View>
            </View>
            {/* Playback Controls */}
            <View style={styles.controls}>
              <Ionicons name="play-skip-back" size={32} color="#FFFFFF" />
              <View style={styles.playButtonContainer}>
                <View style={styles.playButtonBackground} />
                <Ionicons name="pause" size={48} color="#FFFFFF" />
              </View>
              <Ionicons name="play-skip-forward" size={32} color="#FFFFFF" />
            </View>
          </Animated.View>
        )}
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    justifyContent: "space-between",
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  imageContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 30,
  },
  circlesContainer: {
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
  },
  circle: {
    position: "absolute",
    borderWidth: 1,
    borderColor: "rgba(124,77,255,0.4)",
  },
  headphoneImage: {
    width: 200,
    height: 200,
  },
  mainTitle: {
    color: "#FFFFFF",
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 10,
  },
  subtitle: {
    color: "#FFFFFF",
    fontSize: 16,
    marginBottom: 20,
  },
  button: {
    flexDirection: "row",
    backgroundColor: "#7C4DFF",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 24,
    alignItems: "center",
    // Static white glow for the button
    shadowColor: "#FFFFFF",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 10,
    elevation: 10,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    marginRight: 8,
  },
  footer: {
    alignItems: "center",
    marginBottom: 20,
  },
  footerTitle: {
    color: "#FFFFFF",
    fontSize: 14,
  },
  footerSubtitle: {
    color: "#FFFFFF",
    fontSize: 12,
  },
  playerArtwork: {
    width: 240,
    height: 240,
    borderRadius: 16,
    alignSelf: "center",
    marginTop: 32,
  },
  trackInfo: {
    alignItems: "center",
    marginTop: 24,
  },
  trackTitle: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  trackSubtitle: {
    color: "rgba(255,255,255,0.8)",
    fontSize: 16,
  },
  progressContainer: {
    marginTop: 24,
  },
  waveform: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    height: 40,
  },
  waveBar: {
    width: 3,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginHorizontal: 1,
  },
  controls: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 40,
    paddingHorizontal: 24,
  },
  playButtonContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
  },
  playButtonBackground: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    opacity: 0.8,
    backgroundColor: "#7C4DFF",
    shadowColor: "#7C4DFF",
    shadowOpacity: 0.6,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: 0 },
  },
});
