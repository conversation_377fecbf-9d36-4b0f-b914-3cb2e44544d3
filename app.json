{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "son-ykm", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#7C4DFF"}, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE", "WAKE_LOCK"], "networkSecurityConfig": "./src/network_security_config.xml", "package": "com.yakamoz.radio", "usesCleartextTraffic": true, "allowBackup": false}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "baaad0d4-7e6c-4886-8531-77dcc0deaa0c"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/baaad0d4-7e6c-4886-8531-77dcc0deaa0c"}}}